'use client'

import CommonCalender from "@/components/common/CommonCalender"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AppDispatch, RootState } from "@/store"
import { fetchCoachVerification, handleCoachInputChange, postCoachVerification } from "@/store/slices/coach/coachProfileSlice"
import { EachSearchItem } from "@/utils/interfaces"
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"
import { useEffect, useState } from "react"
import { toast } from "react-toastify"

const declarationSchema = z.object({
    accuracy: z.boolean().refine(val => val === true, { message: "You must agree to Document Accuracy & Authenticity" }),
    responsibility: z.boolean().refine(val => val === true, { message: "You must agree to Responsibility for Document Expiration" }),
    ongoing: z.boolean().refine(val => val === true, { message: "You must agree to Ongoing Verification Terms" }),
    consent: z.boolean().refine(val => val === true, { message: "You must agree to Consent to Display Verified Badge" }),
    agreeAll: z.boolean().refine(val => val === true, { message: "You must agree to all terms" }),
    eSign: z.string().min(1, "E-signature is required"),
    date: z.union([z.string().min(1), z.date()]).refine(val => !!val, { message: "Date is required" })

})

type DeclarationFormData = z.infer<typeof declarationSchema>

const CoachDeclaration = () => {
    const { declarations, loading } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()
    const [isSubmitting, setIsSubmitting] = useState(false)

    // Fetch coach verification data on component mount
    useEffect(() => {
        dispatch(fetchCoachVerification())
    }, [dispatch])

    const handleOnChange = (name: string, value: string | File | null | EachSearchItem) => {
        dispatch(handleCoachInputChange({ name: 'declarations', value: { ...declarations, [name]: value } }))
    }

    const {
        register,
        handleSubmit,
        setValue,
        control,
        formState: { errors },
        watch,
        reset,
    } = useForm<DeclarationFormData>({
        resolver: zodResolver(declarationSchema),
        defaultValues: {
            accuracy: declarations?.documentAccuracyAuthenticity || false,
            responsibility: declarations?.responsibilityDocumentExpiration || false,
            ongoing: declarations?.ongoingVerificationTerms || false,
            consent: declarations?.consentDisplayVerifiedBadge || false,
            agreeAll: declarations?.agreeAllTerms || false,
            eSign: declarations?.coachESign || "",
            date: declarations?.eSignDateTime ?? undefined,
        }
    })

    // Update form when declarations data is fetched
    useEffect(() => {
        if (declarations) {
            reset({
                accuracy: declarations?.documentAccuracyAuthenticity || false,
                responsibility: declarations?.responsibilityDocumentExpiration || false,
                ongoing: declarations?.ongoingVerificationTerms || false,
                consent: declarations?.consentDisplayVerifiedBadge || false,
                agreeAll: declarations?.agreeAllTerms || false,
                eSign: declarations?.coachESign || "",
                date: declarations?.eSignDateTime ?? undefined,
            })
        }
    }, [declarations, reset])

    // Check all functionality
    const handleCheckAll = (checked: boolean) => {
        setValue('accuracy', checked)
        setValue('responsibility', checked)
        setValue('ongoing', checked)
        setValue('consent', checked)
        setValue('agreeAll', checked)

        // Update Redux state as well
        dispatch(handleCoachInputChange({
            name: 'declarations',
            value: {
                ...declarations,
                accuracy: checked,
                responsibility: checked,
                ongoing: checked,
                consent: checked,
                agreeAll: checked
            }
        }))
    }

    // Check if all checkboxes are checked
    const allChecked = watch('accuracy') && watch('responsibility') && watch('ongoing') && watch('consent') && watch('agreeAll')

    const onSubmit = async (data: DeclarationFormData) => {
        console.log("Declaration submitted:", data)

        setIsSubmitting(true)

        try {
            // Transform form data to match CoachVerificationPayload interface
            const payload = {
                roleId: 3,
                userId: Number(localStorage.getItem("userId") || 0),
                coachId: 69,
                coachESign: data.eSign,
                eSignDateTime: new Date().toISOString(),
                verifyStartDt: new Date().toISOString(),
                verifyNextStartDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
                documentAccuracyAuthenticity: data.accuracy,
                responsibilityDocumentExpiration: data.responsibility,
                ongoingVerificationTerms: data.ongoing,
                consentDisplayVerifiedBadge: data.consent,
                agreeAllTerms: true,
            }

            const resultAction = await dispatch(postCoachVerification(payload))

            if (postCoachVerification.fulfilled.match(resultAction)) {
                toast.success("Coach verification submitted successfully!")
            } else {
                toast.error("Failed to submit coach verification")
            }
        } catch (error) {
            console.error("Failed to submit verification:", error)
            toast.error("Failed to submit coach verification")
        } finally {
            setIsSubmitting(false)
        }
    }

    console.log(declarations,"declarations")

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="bg-slate-100 p-4 flex flex-col gap-3 rounded-lg">
            <h3 className="text-xl font-bold text-center">Coach Declaration & Acknowledgement</h3>
            <p className="p-4 font-semibold">
                Please read and confirm the following before submitting your verification documents.
            </p>

            {loading && (
                <div className="flex items-center justify-center p-4">
                    <div className="text-gray-600">Loading verification data...</div>
                </div>
            )}

            {/* Check All Button */}
            <div className="flex items-center justify-center p-4">
                <div className="flex items-center gap-2 bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <Checkbox
                        className="border-blue-500"
                        checked={allChecked}
                        onCheckedChange={(checked) => handleCheckAll(!!checked)}
                    />
                    <Label className="font-semibold text-blue-700 cursor-pointer">
                        Check All Terms & Conditions
                    </Label>
                </div>
            </div>

            <div className="flex flex-col p-4 gap-8 rounded-xl">
                {[
                    {
                        id: "accuracy",
                        label: "Document Accuracy & Authenticity",
                        description: "I certify that all documents I have uploaded are accurate, authentic, and belong to me. I understand that submitting false or misleading information may result in removal of my Verified Coach status or suspension from the platform.",
                    },
                    {
                        id: "responsibility",
                        label: "Responsibility for Document Expiration",
                        description: "I acknowledge that any document I upload for verification must be valid and current. If any document expires during my time on the platform, I take full responsibility to upload a renewed version in a timely manner. I understand that failure to do so may result in my Verified Coach badge being revoked.",
                    },
                    {
                        id: "ongoing",
                        label: "Ongoing Verification Terms",
                        description: "I understand that Connect Athlete does not independently verify expiration dates and relies on the information I provide. I agree to maintain the integrity of my verification status by keeping all documentation up to date.",
                    },
                    {
                        id: "consent",
                        label: "Consent to Display Verified Badge",
                        description: "By completing this verification process, I consent to having a Verified Coach badge displayed on my public profile, and I understand that my verified status is subject to review or removal at any time by the platform.",
                    },
                    {
                        id: "agreeAll",
                        label: "",
                        description: "I agree to all the terms above and certify that the information I have submitted is true and accurate.",
                    }
                ].map(({ id, label, description }) => (
                    <div key={id} className="flex gap-2 items-start">
                        <Checkbox
                            className="border-slate-500 mt-1"
                            checked={Boolean(watch(id as keyof DeclarationFormData))}
                            onCheckedChange={(checked) => {
                                setValue(id as keyof DeclarationFormData, !!checked)
                                // Update Redux state
                                dispatch(handleCoachInputChange({
                                    name: 'declarations',
                                    value: { ...declarations, [id]: !!checked }
                                }))
                            }}
                        />
                        <Label>
                            {label && <span className="font-bold">{label}<br /></span>}
                            {description}
                            {errors[id as keyof DeclarationFormData] && (
                                <p className="text-sm text-red-500 mt-1">{errors[id as keyof DeclarationFormData]?.message}</p>
                            )}
                        </Label>
                    </div>
                ))}

                <div className="flex items-center justify-center gap-8">
                    <div className="flex flex-col gap-1">
                        <div className='flex items-center gap-1'>
                            <Checkbox className="border-slate-500" />
                            <Label>[E-Sign Name]</Label>
                        </div>
                        <Input placeholder="Enter name" {...register("eSign")} />
                        {errors.eSign && <p className="text-sm text-red-500">{errors.eSign.message}</p>}
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>[Date]</Label>
                        <Controller
                            name="date"
                            control={control}
                            rules={{ required: true }}
                            defaultValue={declarations?.date ?? undefined}
                            render={({ field }) => (
                                <CommonCalender
                                    placeholder="Pick Date"
                                    mode="single"
                                    dateValue={field.value instanceof Date ? field.value : undefined}
                                    setDateFn={(d) => {
                                        field.onChange(d);               // tell RHF
                                        handleOnChange("date", d);       // update Redux if needed
                                    }}
                                />
                            )}
                        />
                        {errors.date && <p className="text-sm text-red-500">{errors.date.message}</p>}
                    </div>
                </div>

                <div className="flex items-center justify-center">
                    <Button type="submit" disabled={isSubmitting || loading}>
                        {isSubmitting ? "Submitting..." : "Submit"}
                    </Button>
                </div>
            </div>
        </form>
    )
}

export default CoachDeclaration
