
'use client'
import MultiSelectWithChip from "@/components/common/MultiSelectWithChip"
import SearchInput from "@/components/common/SearchInput"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { fetchCoachSports, postAddSport } from "@/store/slices/coach/coachProfileSlice"
import { handleCoachSportInputChange } from "@/store/slices/coach/coachSportSlice"
import { fetchAllSpecialities, fetchAllSportLevels, fetchAllSports } from "@/store/slices/commonSlice"
import { useTokenValues } from "@/hooks/useTokenValues"
import { EachSearchItem, Option } from "@/utils/interfaces"
import { zodResolver } from "@hookform/resolvers/zod"
import { Loader } from "lucide-react"
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"

const coachSportSchema = z.object({
    selectedSport: z
        .any()
        .refine((val) => val !== null && val !== undefined, {
            message: "Sport name is required",
        }),
    togglePrimary: z.boolean(),
    addedSportLevelsList: z
        .array(z.any())
        .min(1, "At least one skill level is required"),
    addedSpecilitiesList: z
        .array(z.any())
        .min(1, "At least one position/speciality is required"),
})

type CoachSportFormData = z.infer<typeof coachSportSchema>

interface CoachSportDetailsProps {
    onClose?: () => void
}

const CoachSportDetails = ({ onClose }: CoachSportDetailsProps) => {
    const { allSportsList, allSportLevelList, allSpecilitiesList } = useSelector((state: RootState) => state.commonSlice)
    const { selectedSport, addedSportLevelsList, addedSpecilitiesList, togglePrimary, loading } = useSelector((state: RootState) => state.coachSport)
    const { coachProfileData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()
    const { userId } = useTokenValues()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const {
        control,
        handleSubmit,
        watch,
        setValue,
        formState: { errors },
        trigger,
    } = useForm<CoachSportFormData>({
        resolver: zodResolver(coachSportSchema),
        mode: "onChange",
        defaultValues: {
            selectedSport: null,
            togglePrimary: true,
            addedSportLevelsList: [],
            addedSpecilitiesList: [],
        },
    })

    const watchedSport = watch("selectedSport")

    useEffect(() => {
        dispatch(fetchAllSportLevels())
        dispatch(fetchAllSports())
    }, [dispatch])

    useEffect(() => {
        watchedSport && dispatch(fetchAllSpecialities(watchedSport?.value))
    }, [dispatch, watchedSport])

    // Sync form with Redux state
    useEffect(() => {
        if (selectedSport) {
            setValue("selectedSport", selectedSport)
        }
        setValue("togglePrimary", togglePrimary)
        setValue("addedSportLevelsList", addedSportLevelsList)
        setValue("addedSpecilitiesList", addedSpecilitiesList)
    }, [selectedSport, togglePrimary, addedSportLevelsList, addedSpecilitiesList, setValue])

    const handleOnChange = (name: string, value: Option[] | EachSearchItem | boolean | null) => {
        dispatch(handleCoachSportInputChange({ name, value }))
    }

    const onSubmit = async (data: CoachSportFormData) => {
        setIsSubmitting(true)
        try {
            const payload = {
                roleId: 3,
                coachId: coachProfileData?.id || Number(userId || 0),
                userId: Number(userId || 0),
                sports:[{
                    sportsId: data.selectedSport?.value,
                    primarySports: data.togglePrimary,
                    levels: data.addedSportLevelsList.map((level: any) => level.value),
                    specialties: data.addedSpecilitiesList.map((speciality: any) => speciality.value),
                    sportsProfileUrl: `coach/profile/${userId}-${coachProfileData?.firstName.replace(/\s/g, '')}-${coachProfileData?.lastName.replace(/\s/g, '')}/${data.selectedSport?.label.replace(/\s/g, '')}/${data.selectedSport?.value}`,
                }],
            }

            const resultAction = await dispatch(postAddSport(payload))
            if (postAddSport.fulfilled.match(resultAction)) {
                // Reset form or handle success
                dispatch(handleCoachSportInputChange({ name: 'selectedSport', value: null }))
                dispatch(handleCoachSportInputChange({ name: 'addedSportLevelsList', value: [] }))
                dispatch(handleCoachSportInputChange({ name: 'addedSpecilitiesList', value: [] }))
                dispatch(handleCoachSportInputChange({ name: 'togglePrimary', value: true }))
                // Close the modal after successful save
                onClose?.()
                // Optionally refresh the coach profile data
                dispatch(fetchCoachSports())
            }
        } catch (error) {
            console.error("Error saving sport details:", error)
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleFormError = (errors: any) => {
        console.log("Form validation errors:", errors)
    }

    return (
        <form onSubmit={handleSubmit(onSubmit, handleFormError)} className="bg-slate-100 rounded-lg p-5">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="flex flex-col gap-1 col-span-2">
                    <Label>Sport Name *</Label>
                    <Controller
                        name="selectedSport"
                        control={control}
                        render={({ field }) => (
                            <SearchInput
                                list={allSportsList}
                                name="selectedSport"
                                placeholder="Select Sport Name"
                                value={field.value}
                                onChange={(name, value) => {
                                    field.onChange(value)
                                    handleOnChange(name, value)
                                }}
                            />
                        )}
                    />
                    {errors.selectedSport && (
                        <p className="text-red-500 text-sm">{errors.selectedSport.message as string}</p>
                    )}
                </div>
                <div className="flex justify-center flex-col gap-1 col-span-1">
                    <Label>Primary Sport</Label>
                    <Controller
                        name="togglePrimary"
                        control={control}
                        render={({ field }) => (
                            <Switch
                                checked={field.value}
                                onCheckedChange={(checked) => {
                                    field.onChange(checked)
                                    handleOnChange('togglePrimary', checked)
                                }}
                            />
                        )}
                    />
                </div>
                <div className="flex flex-col gap-1 col-span-full">
                    <Label>Skill Level(s) I Train *</Label>
                    <Controller
                        name="addedSportLevelsList"
                        control={control}
                        render={({ field }) => (
                            <MultiSelectWithChip
                                options={allSportLevelList}
                                value={field.value}
                                name='addedSportLevelsList'
                                onChange={(selected) => {
                                    field.onChange(selected)
                                    handleOnChange('addedSportLevelsList', selected)
                                }}
                                placeholder="Select Level(s)..."
                            />
                        )}
                    />
                    {errors.addedSportLevelsList && (
                        <p className="text-red-500 text-sm">{errors.addedSportLevelsList.message as string}</p>
                    )}
                </div>
                <div className="flex flex-col gap-1 col-span-full">
                    <Label>Position/Speciality *</Label>
                    <Controller
                        name="addedSpecilitiesList"
                        control={control}
                        render={({ field }) => (
                            <MultiSelectWithChip
                                options={allSpecilitiesList}
                                value={field.value}
                                name='addedSpecilitiesList'
                                onChange={(selected) => {
                                    field.onChange(selected)
                                    handleOnChange('addedSpecilitiesList', selected)
                                }}
                                placeholder="Select..."
                            />
                        )}
                    />
                    {errors.addedSpecilitiesList && (
                        <p className="text-red-500 text-sm">{errors.addedSpecilitiesList.message as string}</p>
                    )}
                </div>
            </div>
            <div className="flex justify-end gap-2 mt-2">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                        // Close the modal when cancel is clicked
                        onClose?.()
                    }}
                >
                    cancel
                </Button>
                <Button
                    type="submit"
                    className="w-24"
                    disabled={isSubmitting || loading}
                >
                    {isSubmitting || loading ? (
                        <>
                            <Loader className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                        </>
                    ) : (
                        "Save"
                    )}
                </Button>
            </div>
        </form>
    )
}
export default CoachSportDetails
